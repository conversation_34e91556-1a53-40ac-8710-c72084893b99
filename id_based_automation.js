// Auto.js ID定位自动填写详解

auto.waitFor();
console.show();

// ===== 基础原理演示 =====

// 1. 通过ID查找元素
function findElementById(elementId) {
    // id() 方法返回一个选择器，用于查找指定ID的元素
    var selector = id(elementId);
    
    // findOne() 查找第一个匹配的元素，会等待直到找到
    var element = selector.findOne();
    
    log("找到元素: " + elementId);
    log("元素类型: " + element.className());
    log("元素文本: " + element.text());
    log("元素坐标: " + element.bounds());
    
    return element;
}

// 2. 输入框填写原理
function fillInputField(elementId, text) {
    try {
        // 步骤1: 找到输入框元素
        var inputElement = id(elementId).findOne(5000); // 等待5秒
        
        if (!inputElement) {
            log("错误: 未找到ID为 " + elementId + " 的元素");
            return false;
        }
        
        // 步骤2: 检查元素是否可编辑
        if (!inputElement.editable()) {
            log("警告: 元素不可编辑");
            // 尝试点击激活
            inputElement.click();
            sleep(500);
        }
        
        // 步骤3: 清空现有内容
        inputElement.setText(""); // 清空
        sleep(200);
        
        // 步骤4: 输入新内容
        var success = inputElement.setText(text);
        
        if (success) {
            log("成功填写: " + elementId + " = " + text);
            
            // 步骤5: 验证输入结果
            var currentText = inputElement.text();
            if (currentText === text) {
                log("验证成功: 输入内容正确");
            } else {
                log("验证失败: 期望=" + text + ", 实际=" + currentText);
            }
        } else {
            log("填写失败: " + elementId);
        }
        
        return success;
        
    } catch (error) {
        log("异常: " + error);
        return false;
    }
}

// 3. 按钮点击原理
function clickButton(elementId) {
    try {
        var button = id(elementId).findOne(5000);
        
        if (!button) {
            log("错误: 未找到按钮 " + elementId);
            return false;
        }
        
        // 检查按钮是否可点击
        if (!button.clickable()) {
            log("警告: 按钮不可点击，尝试坐标点击");
            var bounds = button.bounds();
            return click(bounds.centerX(), bounds.centerY());
        }
        
        // 执行点击
        var success = button.click();
        log("点击按钮: " + elementId + " - " + (success ? "成功" : "失败"));
        
        return success;
        
    } catch (error) {
        log("点击异常: " + error);
        return false;
    }
}

// 4. 下拉框选择原理
function selectDropdown(dropdownId, optionText) {
    try {
        // 步骤1: 找到下拉框
        var dropdown = id(dropdownId).findOne(5000);
        
        if (!dropdown) {
            log("错误: 未找到下拉框 " + dropdownId);
            return false;
        }
        
        // 步骤2: 点击展开下拉框
        dropdown.click();
        sleep(1000); // 等待下拉选项出现
        
        // 步骤3: 查找并点击选项
        // 方法1: 通过文本查找
        var option = text(optionText).findOnce();
        if (!option) {
            // 方法2: 通过包含文本查找
            option = textContains(optionText).findOnce();
        }
        if (!option) {
            // 方法3: 通过描述查找
            option = desc(optionText).findOnce();
        }
        
        if (option) {
            var success = option.click();
            log("选择下拉选项: " + optionText + " - " + (success ? "成功" : "失败"));
            return success;
        } else {
            log("错误: 未找到选项 " + optionText);
            return false;
        }
        
    } catch (error) {
        log("下拉框选择异常: " + error);
        return false;
    }
}

// ===== 实际应用示例 =====

function demonstrateFormFilling() {
    log("=== 表单填写演示 ===");
    
    // 假设我们已经通过元素分析获得了以下ID
    var formConfig = {
        usernameId: "et_username",      // 用户名输入框ID
        passwordId: "et_password",      // 密码输入框ID
        emailId: "et_email",           // 邮箱输入框ID
        genderId: "sp_gender",         // 性别下拉框ID
        submitId: "btn_submit"         // 提交按钮ID
    };
    
    var formData = {
        username: "testuser123",
        password: "password123",
        email: "<EMAIL>",
        gender: "男"
    };
    
    // 执行填写流程
    log("开始填写表单...");
    
    // 1. 填写用户名
    if (fillInputField(formConfig.usernameId, formData.username)) {
        sleep(500); // 等待界面响应
    }
    
    // 2. 填写密码
    if (fillInputField(formConfig.passwordId, formData.password)) {
        sleep(500);
    }
    
    // 3. 填写邮箱
    if (fillInputField(formConfig.emailId, formData.email)) {
        sleep(500);
    }
    
    // 4. 选择性别
    if (selectDropdown(formConfig.genderId, formData.gender)) {
        sleep(500);
    }
    
    // 5. 点击提交
    if (clickButton(formConfig.submitId)) {
        log("表单提交完成");
    }
}

// ===== 高级技巧 =====

// 1. 智能等待元素出现
function waitForElement(elementId, timeout = 10000) {
    var startTime = new Date().getTime();
    
    while (new Date().getTime() - startTime < timeout) {
        var element = id(elementId).findOnce();
        if (element) {
            log("元素已出现: " + elementId);
            return element;
        }
        sleep(100);
    }
    
    log("超时: 元素未出现 " + elementId);
    return null;
}

// 2. 批量填写
function batchFillForm(fieldMappings) {
    for (let field of fieldMappings) {
        log("处理字段: " + field.id);
        
        var element = waitForElement(field.id, 5000);
        if (element) {
            switch (field.type) {
                case "input":
                    fillInputField(field.id, field.value);
                    break;
                case "dropdown":
                    selectDropdown(field.id, field.value);
                    break;
                case "button":
                    clickButton(field.id);
                    break;
            }
            sleep(field.delay || 500);
        }
    }
}

// 使用示例
var formFields = [
    {id: "et_username", type: "input", value: "testuser", delay: 300},
    {id: "et_password", type: "input", value: "password123", delay: 300},
    {id: "sp_city", type: "dropdown", value: "北京", delay: 500},
    {id: "btn_submit", type: "button", value: null, delay: 1000}
];

// 执行演示
log("=== 开始ID定位自动填写演示 ===");
// demonstrateFormFilling();
// batchFillForm(formFields);

log("=== 演示脚本加载完成 ===");
log("请根据实际页面的ID修改配置后运行相应函数");
