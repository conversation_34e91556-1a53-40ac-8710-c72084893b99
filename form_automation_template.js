// Auto.js 通用表单自动化模板
// 适用于不知道具体ID的情况

auto.waitFor();
console.show();

// 配置区域 - 根据实际情况修改
var config = {
    // 表单数据
    formData: {
        username: "your_username",
        password: "your_password",
        email: "<EMAIL>",
        phone: "13800138000"
    },
    
    // 等待时间配置
    waitTime: {
        pageLoad: 3000,
        elementLoad: 1000,
        afterClick: 500
    }
};

// 工具函数
function waitAndFindElement(selector, timeout = 5000) {
    var startTime = new Date().getTime();
    while (new Date().getTime() - startTime < timeout) {
        var element = selector.findOnce();
        if (element) {
            return element;
        }
        sleep(100);
    }
    return null;
}

function safeClick(element) {
    if (element && element.clickable()) {
        return element.click();
    } else if (element) {
        // 如果元素不可点击，尝试坐标点击
        var bounds = element.bounds();
        return click(bounds.centerX(), bounds.centerY());
    }
    return false;
}

function fillInputByIndex(index, value) {
    var inputs = className("android.widget.EditText").find();
    if (inputs.length > index) {
        inputs[index].setText(value);
        log("已填写第" + (index + 1) + "个输入框: " + value);
        return true;
    }
    return false;
}

function fillInputByHint(hint, value) {
    var input = className("android.widget.EditText").hint(hint).findOnce();
    if (input) {
        input.setText(value);
        log("已填写提示为'" + hint + "'的输入框: " + value);
        return true;
    }
    return false;
}

function fillInputByText(text, value) {
    var input = className("android.widget.EditText").text(text).findOnce();
    if (input) {
        input.setText(value);
        log("已填写文本为'" + text + "'的输入框: " + value);
        return true;
    }
    return false;
}

function clickButtonByText(buttonText) {
    var button = text(buttonText).findOnce() || 
                 textContains(buttonText).findOnce() ||
                 desc(buttonText).findOnce();
    
    if (button) {
        safeClick(button);
        log("已点击按钮: " + buttonText);
        return true;
    }
    return false;
}

function selectDropdownOption(dropdownIndex, optionText) {
    var dropdowns = className("android.widget.Spinner").find();
    if (dropdowns.length > dropdownIndex) {
        // 点击下拉框
        safeClick(dropdowns[dropdownIndex]);
        sleep(config.waitTime.afterClick);
        
        // 选择选项
        var option = text(optionText).findOnce() || textContains(optionText).findOnce();
        if (option) {
            safeClick(option);
            log("已选择下拉框选项: " + optionText);
            return true;
        }
    }
    return false;
}

// 主要自动化流程
function automateForm() {
    log("=== 开始表单自动化 ===");
    
    // 等待页面加载
    sleep(config.waitTime.pageLoad);
    
    // 方法1: 按索引填写（最通用）
    log("\n--- 尝试按索引填写表单 ---");
    var inputs = className("android.widget.EditText").find();
    log("发现 " + inputs.length + " 个输入框");
    
    if (inputs.length >= 2) {
        fillInputByIndex(0, config.formData.username);
        sleep(config.waitTime.afterClick);
        fillInputByIndex(1, config.formData.password);
        sleep(config.waitTime.afterClick);
    }
    
    // 方法2: 按提示文本填写
    log("\n--- 尝试按提示文本填写 ---");
    fillInputByHint("用户名", config.formData.username) ||
    fillInputByHint("账号", config.formData.username) ||
    fillInputByHint("手机号", config.formData.phone) ||
    fillInputByHint("邮箱", config.formData.email);
    
    fillInputByHint("密码", config.formData.password) ||
    fillInputByHint("口令", config.formData.password);
    
    // 方法3: 按坐标填写（需要手动调整坐标）
    log("\n--- 备用坐标方案 ---");
    // click(300, 400); // 第一个输入框坐标
    // setText(config.formData.username);
    // click(300, 500); // 第二个输入框坐标  
    // setText(config.formData.password);
    
    // 处理下拉框
    log("\n--- 处理下拉框 ---");
    selectDropdownOption(0, "选项1"); // 第一个下拉框选择"选项1"
    
    // 点击提交按钮
    log("\n--- 点击提交按钮 ---");
    var submitSuccess = clickButtonByText("登录") ||
                       clickButtonByText("提交") ||
                       clickButtonByText("确定") ||
                       clickButtonByText("下一步");
    
    if (!submitSuccess) {
        log("未找到提交按钮，尝试点击最后一个按钮");
        var buttons = className("android.widget.Button").find();
        if (buttons.length > 0) {
            safeClick(buttons[buttons.length - 1]);
        }
    }
    
    log("=== 表单自动化完成 ===");
}

// 执行自动化
try {
    automateForm();
    toast("自动化执行完成");
} catch (error) {
    log("执行出错: " + error);
    toast("执行出错，请查看日志");
}
