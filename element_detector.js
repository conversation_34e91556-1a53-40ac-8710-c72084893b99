// Auto.js 元素探测脚本
// 用于分析当前页面的所有可操作元素

auto.waitFor();
console.show();

// 等待用户准备
toast("3秒后开始分析页面元素...");
sleep(3000);

log("=== 开始分析页面元素 ===");

// 1. 获取基本信息
log("当前应用包名: " + currentPackage());
log("当前活动: " + currentActivity());

// 2. 查找所有输入框
log("\n=== 输入框信息 ===");
var editTexts = className("android.widget.EditText").find();
for(let i = 0; i < editTexts.length; i++){
    var et = editTexts[i];
    log("输入框" + (i+1) + ":");
    log("  ID: " + (et.id() || "无"));
    log("  文本: " + (et.text() || "无"));
    log("  提示: " + (et.hint() || "无"));
    log("  坐标: " + et.bounds());
    log("  类名: " + et.className());
    log("---");
}

// 3. 查找所有按钮
log("\n=== 按钮信息 ===");
var buttons = className("android.widget.Button").find();
for(let i = 0; i < buttons.length; i++){
    var btn = buttons[i];
    log("按钮" + (i+1) + ":");
    log("  ID: " + (btn.id() || "无"));
    log("  文本: " + (btn.text() || "无"));
    log("  坐标: " + btn.bounds());
    log("---");
}

// 4. 查找所有可点击元素
log("\n=== 可点击元素 ===");
var clickables = clickable(true).find();
for(let i = 0; i < Math.min(clickables.length, 10); i++){ // 限制显示前10个
    var el = clickables[i];
    log("可点击元素" + (i+1) + ":");
    log("  ID: " + (el.id() || "无"));
    log("  文本: " + (el.text() || "无"));
    log("  类名: " + el.className());
    log("  坐标: " + el.bounds());
    log("---");
}

// 5. 查找下拉框/选择器
log("\n=== 下拉框信息 ===");
var spinners = className("android.widget.Spinner").find();
for(let i = 0; i < spinners.length; i++){
    var spinner = spinners[i];
    log("下拉框" + (i+1) + ":");
    log("  ID: " + (spinner.id() || "无"));
    log("  文本: " + (spinner.text() || "无"));
    log("  坐标: " + spinner.bounds());
    log("---");
}

// 6. 查找网页元素（如果是WebView）
try {
    var webViews = className("android.webkit.WebView").find();
    if(webViews.length > 0) {
        log("\n=== 发现WebView ===");
        log("页面可能是网页，建议使用浏览器开发者工具进一步分析");
    }
} catch(e) {
    // 忽略错误
}

log("\n=== 分析完成 ===");
toast("元素分析完成，请查看日志");

// 保存日志到文件
var logContent = console.getLog();
files.write("/sdcard/element_analysis.txt", logContent);
toast("日志已保存到 /sdcard/element_analysis.txt");
